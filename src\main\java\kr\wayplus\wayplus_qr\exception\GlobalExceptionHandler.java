package kr.wayplus.wayplus_qr.exception;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ErrorDetail;
import kr.wayplus.wayplus_qr.exception.FileStorageException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.AccessDeniedException;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice 
public class GlobalExceptionHandler {

    // 인증 실패 (아이디/비밀번호 오류 등)
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponseDto<?>> handleBadCredentialsException(BadCredentialsException e) { 
        log.warn("Authentication failed: {}", e.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED") 
                .message("아이디 또는 비밀번호가 잘못되었습니다.")
                .build();
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.UNAUTHORIZED); 
    }

    // 잘못된 인자 (유효하지 않은 토큰 등)
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponseDto<?>> handleIllegalArgumentException(IllegalArgumentException e) { 
        log.warn("Invalid argument: {}", e.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED") 
                .message("입력값이 유효하지 않습니다.")
                .build();
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.BAD_REQUEST); 
    }

    // 유효하지 않은 토큰 예외 처리
    @ExceptionHandler(InvalidTokenException.class)
    public ResponseEntity<ApiResponseDto<?>> handleInvalidTokenException(InvalidTokenException ex) {
        log.warn("Invalid token error: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED")
                .message("유효하지 않은 인증 토큰입니다.")
                .build();
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.UNAUTHORIZED); 
    }

    // 유효성 검사 실패 예외 처리
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponseDto<?>> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.warn("Validation error: {}", ex.getMessage());
        
        // 에러 메시지 추출
        String errorMessage = "입력값 검증에 실패했습니다.";
        
        // 필드 에러가 있는 경우 첫 번째 에러 메시지를 가져옴
        if (ex.getBindingResult().hasFieldErrors()) {
            FieldError fieldError = ex.getBindingResult().getFieldErrors().get(0);
            errorMessage = fieldError.getDefaultMessage();
        }
        
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED")
                .message(errorMessage)
                .build();
        
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.BAD_REQUEST);
    }
    
    // 만료된 토큰 예외 처리
    @ExceptionHandler(ExpiredTokenException.class)
    public ResponseEntity<ApiResponseDto<?>> handleExpiredTokenException(ExpiredTokenException ex) {
        log.warn("Expired token error: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED")
                .message("인증 토큰이 만료되었습니다. 다시 로그인해주세요.")
                .build();
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.UNAUTHORIZED); 
    }

    // 현재 비밀번호 불일치 (InvalidCurrentPasswordException)
    @ExceptionHandler(InvalidCurrentPasswordException.class)
    public ResponseEntity<ApiResponseDto<?>> handleInvalidCurrentPasswordException(InvalidCurrentPasswordException ex) {
        log.warn("Invalid current password during password change: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("INVALID_CURRENT_PASSWORD") // 새로운 에러 코드
                .message(ex.getMessage()) // 예외 객체에서 메시지 가져오기
                .build();
        // 현재 비밀번호 오류는 '인증 실패'와 유사하게 401 반환
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.UNAUTHORIZED);
    }

    // 계정이 비활성화 되었을 때 (DisabledException)
    @ExceptionHandler(DisabledException.class)
    public ResponseEntity<ApiResponseDto<?>> handleDisabledException(DisabledException ex) {
        log.warn("Authentication failed - Account disabled: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(ErrorCode.ACCOUNT_DISABLED.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(ErrorCode.ACCOUNT_DISABLED.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 데이터베이스 제약 조건 위반 (예: Unique 키 중복) 시 처리합니다.
    // UserService에서 DuplicateKeyException을 발생시키는 경우 이 핸들러가 호출됩니다.
    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<ApiResponseDto<Void>> handleDuplicateKeyException(DuplicateKeyException ex) {
        log.warn("Handling DuplicateKeyException: {}", ex.getMessage()); // 로그 기록

        String errorMessage = ErrorCode.DATABASE_DUPLICATE_ERROR.getMessage();
        String errorCode = ErrorCode.DATABASE_DUPLICATE_ERROR.getCode();

        // 메뉴 코드 중복인 경우 더 구체적인 메시지 제공
        if (ex.getMessage() != null && ex.getMessage().contains("menu_code")) {
            errorMessage = "이미 존재하는 메뉴 코드입니다. 다른 코드를 사용해주세요.";
            errorCode = ErrorCode.DUPLICATE_MENU_CODE.getCode();
        }
        // 메뉴 이름 중복인 경우
        else if (ex.getMessage() != null && ex.getMessage().contains("menu_name")) {
            errorMessage = "이미 존재하는 메뉴 이름입니다. 다른 이름을 사용해주세요.";
            errorCode = ErrorCode.DUPLICATE_MENU_NAME.getCode();
        }

        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode)
                .message(errorMessage)
                .build();

        return ResponseEntity
                .status(HttpStatus.CONFLICT) // 409 Conflict 상태 코드 사용
                .body(ApiResponseDto.error(errorDetail));
    }

    // 권한 없음 예외 처리 (AccessDeniedException, AuthorizationDeniedException 통합 처리)
    @ExceptionHandler({AccessDeniedException.class, AuthorizationDeniedException.class})
    public ResponseEntity<ApiResponseDto<?>> handleAccessDeniedException(Exception ex) { // 파라미터 타입을 Exception으로 변경
        log.warn("Access denied: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(ErrorCode.ACCESS_DENIED.getCode())
                .message("접근 권한이 없습니다.")
                .build();
        return ResponseEntity.status(ErrorCode.ACCESS_DENIED.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 비밀번호 변경 필요 예외 처리
    @ExceptionHandler(PasswordChangeRequiredException.class)
    public ResponseEntity<ApiResponseDto<Object>> handlePasswordChangeRequiredException(PasswordChangeRequiredException ex) {
        log.warn("Password change required for user: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("PASSWORD_CHANGE_REQUIRED")
                .message(ex.getMessage())
                .build();
        ApiResponseDto<Object> apiResponse = ApiResponseDto.error(errorDetail);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(apiResponse);
    }

    // 프로젝트 관련 예외 처리
    @ExceptionHandler(CustomProjectException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleCustomProjectException(CustomProjectException ex) {
        log.warn("Project exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // QR코드 예외 처리
    @ExceptionHandler(QRcodeException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleQrCodeException(QRcodeException ex) {
        log.warn("QR code exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 랜딩페이지 예외 처리
    @ExceptionHandler(CustomLandingPageException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleCustomLandingPageException(CustomLandingPageException ex) {
        log.warn("Landing page exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // (프로젝트)이벤트 예외 처리
    @ExceptionHandler(CustomEventException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleCustomEventException(CustomEventException ex) {
        log.warn("Event exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 사전 신청서 예외 처리
    @ExceptionHandler(CustomPreRegistrationFormException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleCustomPreRegistrationFormException(CustomPreRegistrationFormException ex) {
        log.warn("Pre-registration form exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 참가자 관련 예외 처리
    @ExceptionHandler(CustomAttendeeException.class) // 통합 핸들러
    public ResponseEntity<ApiResponseDto<?>> handleCustomAttendeeException(CustomAttendeeException ex) {
        log.warn("Attendee exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 통계 관련 예외 처리
    @ExceptionHandler(CustomStatisticsException.class)
    public ResponseEntity<ApiResponseDto<?>> handleCustomStatisticsException(CustomStatisticsException ex) {
        log.warn("Statistics exception occurred: {} - {}", ex.getErrorCode(), ex.getMessage());
        ErrorCode errorCode = ex.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(errorCode.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 파일 저장 예외 처리
    @ExceptionHandler(FileStorageException.class)
    public ResponseEntity<ApiResponseDto<?>> handleFileStorageException(FileStorageException ex) {
        log.error("File storage error: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("FILE_STORAGE_ERROR")
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseDto.error(errorDetail));
    }

    // 파일 찾기 예외 처리
    @ExceptionHandler(FileNotFoundException.class)
    public ResponseEntity<ApiResponseDto<?>> handleFileNotFoundException(FileNotFoundException ex) {
        log.error("File not found: {}", ex.getMessage());
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("FILE_NOT_FOUND")
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponseDto.error(errorDetail));
    }

    //CustomException
    @ExceptionHandler(CustomException.class)
    public ResponseEntity<ApiResponseDto<?>> CustomException(CustomException e) { 
        log.error("CustomException occurred: ", e);
        ErrorCode errorCode = e.getErrorCode();
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code(errorCode.getCode())
                .message(e.getMessage())
                .build();
        return ResponseEntity.status(errorCode.getStatus())
                .body(ApiResponseDto.error(errorDetail));
    }

    // 기타 처리되지 않은 예외
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponseDto<?>> handleException(Exception e) { 
        log.error("Unhandled exception occurred: ", e);
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("AUTHENTICATION_FAILED") 
                .message("서버 오류가 발생하였습니다. 관리자에게 문의하세요.")
                .build();
        return new ResponseEntity<>(ApiResponseDto.error(errorDetail), HttpStatus.INTERNAL_SERVER_ERROR); 
    }
}

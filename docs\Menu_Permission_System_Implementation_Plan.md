# 메뉴 기반 API 접근 제한 시스템 구현 계획

## 📋 개요
사용자별 메뉴별 CRUD 권한을 세밀하게 제어하는 시스템 구현 계획서

**생성일**: 2025-07-02  
**대상**: 백엔드/프론트엔드 개발자  
**권한 우선순위**: 개별 사용자 메뉴 권한 > 역할별 메뉴 권한 > 사용자 전역 권한 > 전역 기본 권한

---

## 🎯 구현 목표

### 핵심 기능
- **전역 기본 권한**: 모든 사용자의 기본 CRUD 권한 설정
- **사용자별 전역 권한**: 특정 사용자의 모든 메뉴에 대한 권한 오버라이드
- **메뉴별 역할 권한**: 역할별로 특정 메뉴의 CRUD 권한 설정
- **메뉴별 개별 사용자 권한**: 개별 사용자의 특정 메뉴 권한 설정
- **API 접근 제어**: 설정된 권한에 따른 실제 API 접근 차단

### 사용 시나리오 예시
- **A사용자**: 모든 메뉴에 읽기(R)만 가능
- **B사용자**: QR관리 메뉴만 RUD 가능, 나머지는 R만 가능
- **C사용자**: 프로젝트관리는 CRUD, 사용자관리는 RU, 나머지는 R만

---

## 🗄️ 데이터베이스 구조

### 새로 생성할 테이블
1. **global_default_permissions**: 전역 기본 권한 설정
2. **user_global_permissions**: 사용자별 전역 권한 오버라이드
3. **menu_api_mappings**: 메뉴-API 매핑 정보

### 수정할 기존 테이블
1. **manage_menu_role_permissions**: CRUD 권한 컬럼 추가
2. **manage_menu_user_permissions**: CRUD 권한 컬럼 추가

**DDL 파일**: `src/main/resources/sql/DDL/menu_permission_system_DDL.sql`

---

## 🔧 백엔드 구현 계획

### 1단계: 엔티티 및 DTO 확장
- [ ] MenuEntity에 권한 관련 클래스 추가
- [ ] MenuDto에 권한 응답 DTO 추가
- [ ] 새로운 권한 관리 DTO 생성

### 2단계: 매퍼 및 서비스 확장
- [ ] ManageMenuMapper에 권한 관련 메서드 추가
- [ ] 새로운 PermissionService 생성
- [ ] 권한 검증 로직 구현

### 3단계: 인터셉터 구현
- [ ] MenuBasedAccessInterceptor 생성
- [ ] API 패턴 매칭 로직 구현
- [ ] 권한 검증 및 차단 로직 구현

### 4단계: 컨트롤러 구현
- [ ] PermissionController 생성
- [ ] 권한 관리 API 엔드포인트 구현

### 5단계: 성능 최적화
- [ ] 권한 정보 캐싱 구현
- [ ] API 매핑 정보 캐싱 구현

---

## 🌐 프론트엔드 API 가이드

### 기본 정보
- **Base URL**: `/manage/permissions`
- **인증**: JWT Bearer Token 필요
- **권한**: 모든 권한 관리 API는 SUPER_ADMIN 권한 필요
- **Content-Type**: `application/json`

---

## 📡 API 엔드포인트 상세

### 1. 전역 기본 권한 관리

#### 1.1 전역 기본 권한 조회
**기능**: 시스템의 기본 CRUD 권한 설정을 조회합니다.

```http
GET /manage/permissions/global/default
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "canRead": "Y",
    "canWrite": "Y",
    "canUpdate": "Y", 
    "canDelete": "Y",
    "description": "시스템 기본 권한",
    "lastUpdateDate": "2025-01-01 10:00:00"
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 1.2 전역 기본 권한 수정
**기능**: 시스템의 기본 CRUD 권한을 수정합니다.

```http
PUT /manage/permissions/global/default
```

**요청 형식**:
```json
{
  "canRead": "Y",
  "canWrite": "N",
  "canUpdate": "N",
  "canDelete": "N", 
  "description": "보안 강화를 위한 기본 권한 제한"
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": null,
  "message": "전역 기본 권한이 성공적으로 수정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

### 2. 사용자별 전역 권한 관리

#### 2.1 사용자 전역 권한 목록 조회
**기능**: 전역 권한이 설정된 사용자 목록을 페이징으로 조회합니다.

```http
GET /manage/permissions/users/global?page=0&size=10&search=<EMAIL>
```

**쿼리 파라미터**:
- `page`: 페이지 번호 (0부터 시작)
- `size`: 페이지 크기 (기본값: 10)
- `search`: 사용자 이메일 검색어 (선택사항)

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "userEmail": "<EMAIL>",
        "userName": "사용자A",
        "canRead": "Y",
        "canWrite": "N",
        "canUpdate": "N",
        "canDelete": "N",
        "permissionNote": "읽기 전용 사용자",
        "createDate": "2025-01-01 10:00:00",
        "lastUpdateDate": "2025-01-01 10:00:00"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 2.2 사용자 전역 권한 설정
**기능**: 특정 사용자의 전역 권한을 새로 설정합니다.

```http
POST /manage/permissions/users/{userEmail}/global
```

**경로 변수**:
- `userEmail`: 대상 사용자 이메일

**요청 형식**:
```json
{
  "canRead": "Y",
  "canWrite": "N",
  "canUpdate": "N", 
  "canDelete": "N",
  "permissionNote": "읽기 전용 사용자로 설정"
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "userEmail": "<EMAIL>",
    "canRead": "Y",
    "canWrite": "N",
    "canUpdate": "N",
    "canDelete": "N",
    "permissionNote": "읽기 전용 사용자로 설정"
  },
  "message": "사용자 전역 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 2.3 사용자 전역 권한 수정
**기능**: 기존 사용자의 전역 권한을 수정합니다.

```http
PUT /manage/permissions/users/{userEmail}/global
```

**요청/응답 형식**: 2.2와 동일

#### 2.4 사용자 전역 권한 삭제
**기능**: 사용자의 전역 권한 설정을 삭제하여 기본값으로 복원합니다.

```http
DELETE /manage/permissions/users/{userEmail}/global
```

**응답 형식**:
```json
{
  "success": true,
  "data": null,
  "message": "사용자 전역 권한이 삭제되어 기본값으로 복원되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

### 3. 메뉴별 역할 권한 관리

#### 3.1 특정 메뉴의 역할별 권한 조회
**기능**: 특정 메뉴에 대한 모든 역할의 권한 설정을 조회합니다.

```http
GET /manage/permissions/menus/{menuId}/roles
```

**경로 변수**:
- `menuId`: 메뉴 ID

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "menuCode": "QR_MANAGEMENT",
    "menuName": "QR 관리",
    "rolePermissions": [
      {
        "roleId": "PROJECT_ADMIN",
        "roleName": "프로젝트 관리자",
        "isAccessible": "Y",
        "canRead": "Y",
        "canWrite": "Y",
        "canUpdate": "Y",
        "canDelete": "Y",
        "createDate": "2025-01-01 10:00:00"
      },
      {
        "roleId": "SUB_ADMIN",
        "roleName": "서브 관리자",
        "isAccessible": "Y",
        "canRead": "Y",
        "canWrite": "Y",
        "canUpdate": "N",
        "canDelete": "N",
        "createDate": "2025-01-01 10:00:00"
      }
    ]
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 3.2 메뉴별 역할 권한 일괄 설정
**기능**: 특정 메뉴에 대한 여러 역할의 권한을 일괄로 설정합니다.

```http
POST /manage/permissions/menus/{menuId}/roles/batch
```

**요청 형식**:
```json
{
  "rolePermissions": [
    {
      "roleId": "PROJECT_ADMIN",
      "isAccessible": "Y",
      "canRead": "Y",
      "canWrite": "Y",
      "canUpdate": "Y",
      "canDelete": "Y"
    },
    {
      "roleId": "SUB_ADMIN",
      "isAccessible": "Y",
      "canRead": "Y",
      "canWrite": "Y",
      "canUpdate": "N",
      "canDelete": "N"
    }
  ]
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "updatedRoles": ["PROJECT_ADMIN", "SUB_ADMIN"],
    "totalUpdated": 2
  },
  "message": "메뉴 역할 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

### 4. 메뉴별 개별 사용자 권한 관리

#### 4.1 특정 메뉴의 사용자별 권한 조회
**기능**: 특정 메뉴에 개별 권한이 설정된 사용자 목록을 조회합니다.

```http
GET /manage/permissions/menus/{menuId}/users?page=0&size=10&search=<EMAIL>
```

**쿼리 파라미터**:
- `page`: 페이지 번호 (0부터 시작)
- `size`: 페이지 크기 (기본값: 10)
- `search`: 사용자 이메일 검색어 (선택사항)

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "menuCode": "QR_MANAGEMENT",
    "menuName": "QR 관리",
    "userPermissions": {
      "content": [
        {
          "userEmail": "<EMAIL>",
          "userName": "사용자B",
          "isAccessible": "Y",
          "canRead": "Y",
          "canWrite": "N",
          "canUpdate": "Y",
          "canDelete": "Y",
          "permissionNote": "QR 관리 특별 권한",
          "createDate": "2025-01-01 10:00:00"
        }
      ],
      "totalElements": 1,
      "totalPages": 1,
      "size": 10,
      "number": 0
    }
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 4.2 메뉴별 사용자 권한 설정
**기능**: 특정 메뉴에 대한 개별 사용자 권한을 설정합니다.

```http
POST /manage/permissions/menus/{menuId}/users/{userEmail}
```

**경로 변수**:
- `menuId`: 메뉴 ID
- `userEmail`: 대상 사용자 이메일

**요청 형식**:
```json
{
  "isAccessible": "Y",
  "canRead": "Y",
  "canWrite": "N",
  "canUpdate": "Y",
  "canDelete": "Y",
  "permissionNote": "QR 관리 특별 권한 부여"
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "userEmail": "<EMAIL>",
    "isAccessible": "Y",
    "canRead": "Y",
    "canWrite": "N",
    "canUpdate": "Y",
    "canDelete": "Y",
    "permissionNote": "QR 관리 특별 권한 부여"
  },
  "message": "메뉴 사용자 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 4.3 메뉴별 사용자 권한 수정
**기능**: 기존 메뉴별 사용자 권한을 수정합니다.

```http
PUT /manage/permissions/menus/{menuId}/users/{userEmail}
```

**요청/응답 형식**: 4.2와 동일

#### 4.4 메뉴별 사용자 권한 삭제
**기능**: 특정 메뉴의 개별 사용자 권한을 삭제합니다.

```http
DELETE /manage/permissions/menus/{menuId}/users/{userEmail}
```

**응답 형식**:
```json
{
  "success": true,
  "data": null,
  "message": "메뉴 사용자 권한이 성공적으로 삭제되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

### 5. 통합 권한 조회 API

#### 5.1 특정 사용자의 모든 메뉴 권한 조회
**기능**: 특정 사용자의 모든 메뉴에 대한 최종 권한을 조회합니다.

```http
GET /manage/permissions/users/{userEmail}/menus
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "userEmail": "<EMAIL>",
    "userName": "사용자B",
    "globalPermissions": {
      "canRead": "Y",
      "canWrite": "N",
      "canUpdate": "N",
      "canDelete": "N",
      "permissionNote": "기본 읽기 전용"
    },
    "menuPermissions": [
      {
        "menuId": 1,
        "menuCode": "QR_MANAGEMENT",
        "menuName": "QR 관리",
        "finalPermissions": {
          "canRead": "Y",
          "canWrite": "N",
          "canUpdate": "Y",
          "canDelete": "Y"
        },
        "permissionSource": "user_specific"
      },
      {
        "menuId": 2,
        "menuCode": "PROJECT_MANAGEMENT",
        "menuName": "프로젝트 관리",
        "finalPermissions": {
          "canRead": "Y",
          "canWrite": "N",
          "canUpdate": "N",
          "canDelete": "N"
        },
        "permissionSource": "global"
      }
    ]
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

**permissionSource 값**:
- `global`: 전역 권한 적용
- `role`: 역할별 권한 적용
- `user_specific`: 개별 사용자 권한 적용

#### 5.2 권한 매트릭스 조회
**기능**: 여러 사용자의 여러 메뉴에 대한 권한을 매트릭스 형태로 조회합니다.

```http
GET /manage/permissions/matrix?userEmails=<EMAIL>,<EMAIL>&menuCodes=QR_MANAGEMENT,PROJECT_MANAGEMENT
```

**쿼리 파라미터**:
- `userEmails`: 조회할 사용자 이메일 목록 (쉼표로 구분)
- `menuCodes`: 조회할 메뉴 코드 목록 (쉼표로 구분)

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "matrix": [
      {
        "userEmail": "<EMAIL>",
        "userName": "사용자1",
        "permissions": {
          "QR_MANAGEMENT": {
            "canRead": "Y",
            "canWrite": "N",
            "canUpdate": "N",
            "canDelete": "N",
            "source": "global"
          },
          "PROJECT_MANAGEMENT": {
            "canRead": "Y",
            "canWrite": "Y",
            "canUpdate": "Y",
            "canDelete": "N",
            "source": "role"
          }
        }
      },
      {
        "userEmail": "<EMAIL>",
        "userName": "사용자2",
        "permissions": {
          "QR_MANAGEMENT": {
            "canRead": "Y",
            "canWrite": "N",
            "canUpdate": "Y",
            "canDelete": "Y",
            "source": "user_specific"
          },
          "PROJECT_MANAGEMENT": {
            "canRead": "Y",
            "canWrite": "N",
            "canUpdate": "N",
            "canDelete": "N",
            "source": "global"
          }
        }
      }
    ]
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

### 6. 기존 메뉴 API 확장

#### 6.1 권한 정보 포함 메뉴 트리 조회
**기능**: 현재 사용자의 접근 가능한 메뉴와 각 메뉴별 CRUD 권한을 함께 조회합니다.

```http
GET /manage/menus/accessible
```

**응답 형식** (기존 API 확장):
```json
{
  "success": true,
  "data": [
    {
      "menuId": 1,
      "parentMenuId": null,
      "menuCode": "QR_MANAGEMENT",
      "menuName": "QR 관리",
      "menuUrl": "/qr-management",
      "menuIcon": "fas fa-qrcode",
      "menuLevel": 1,
      "displayOrder": 1,
      "status": "ACTIVE",
      "accessible": true,
      "permissions": {
        "canRead": true,
        "canWrite": false,
        "canUpdate": true,
        "canDelete": false
      },
      "children": []
    }
  ],
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

---

## 🎨 프론트엔드 구현 가이드

### 권한 체크 유틸리티
```javascript
class PermissionChecker {
  constructor(menuPermissions) {
    this.permissions = new Map();
    menuPermissions.forEach(menu => {
      this.permissions.set(menu.menuCode, menu.permissions);
    });
  }

  canAccess(menuCode, permission = 'read') {
    const menuPerms = this.permissions.get(menuCode);
    if (!menuPerms) return false;

    const permKey = `can${permission.charAt(0).toUpperCase() + permission.slice(1)}`;
    return menuPerms[permKey] || false;
  }
}
```

### 사용 예시
```javascript
// 권한 정보 로드
const response = await fetch('/manage/menus/accessible');
const menuData = await response.json();
const permissionChecker = new PermissionChecker(menuData.data);

// 권한 체크
const canCreateQR = permissionChecker.canAccess('QR_MANAGEMENT', 'write');
const canEditQR = permissionChecker.canAccess('QR_MANAGEMENT', 'update');
const canDeleteQR = permissionChecker.canAccess('QR_MANAGEMENT', 'delete');
```

---

## 🚀 구현 순서

### Phase 1: 기반 구조 (1주)
1. DDL 실행 및 테이블 생성
2. 엔티티 및 DTO 확장
3. 매퍼 인터페이스 확장

### Phase 2: 권한 관리 API (1주)
1. PermissionService 구현
2. PermissionController 구현
3. 권한 관리 API 테스트

### Phase 3: API 접근 제어 (1주)
1. MenuBasedAccessInterceptor 구현
2. API 패턴 매칭 로직 구현
3. 권한 검증 로직 구현

### Phase 4: 성능 최적화 및 테스트 (1주)
1. 캐싱 구현
2. 통합 테스트
3. 성능 테스트 및 최적화

---

## 📝 주의사항

### 보안
- 모든 권한 관리 API는 SUPER_ADMIN 권한 필요
- 프론트엔드 권한 체크는 UX용, 실제 보안은 백엔드에서 담당
- 권한 정보 캐싱 시 보안 고려

### 성능
- 권한 검증은 모든 API 호출 시 실행되므로 성능 최적화 필수
- 캐싱 전략 수립 및 적용
- DB 조회 최소화

### 호환성
- 기존 `@PreAuthorize` 어노테이션과 병행 사용
- 기존 메뉴 시스템과의 호환성 유지

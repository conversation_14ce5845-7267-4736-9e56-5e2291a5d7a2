# 메뉴별 사용자 권한 관리 시스템 구현 계획

## 📋 개요
관리자페이지에서 특정 메뉴의 상세페이지에서 사용자별 CRUD 권한을 관리하는 시스템

**생성일**: 2025-07-02
**대상**: 백엔드/프론트엔드 개발자

---

## 🎯 구현 목표

### 핵심 기능
**메뉴 상세페이지에서 사용자별 CRUD 권한 관리**
- 특정 메뉴 선택 → 해당 메뉴에 대한 모든 사용자의 CRUD 권한 조회/설정
- 개별 사용자별로 읽기(R), 쓰기(C), 수정(U), 삭제(D) 권한 개별 설정
- 설정된 권한에 따른 실제 API 접근 차단

### 사용 시나리오
1. 관리자가 "QR 관리" 메뉴 상세페이지 접근
2. 해당 메뉴에 대한 모든 사용자의 현재 권한 상태 확인
3. 특정 사용자의 권한 수정 (예: A사용자는 읽기만, B사용자는 읽기+수정만)
4. 설정 저장 후 실제 API 호출 시 권한에 따라 접근 제어

---

## 🗄️ 데이터베이스 구조

### 필요한 테이블
1. **기존 테이블 확장**: `manage_menu_user_permissions`에 CRUD 권한 컬럼 추가
2. **새 테이블**: `menu_api_mappings` (메뉴-API 매핑)

**DDL 파일**: `src/main/resources/sql/DDL/menu_permission_system_DDL.sql`

---

## 🔧 백엔드 구현 계획

### 1단계: 기본 구조 (1주)
- [ ] 기존 테이블에 CRUD 권한 컬럼 추가
- [ ] 메뉴-API 매핑 테이블 생성
- [ ] MenuDto에 권한 응답 DTO 추가

### 2단계: 권한 관리 API (1주)
- [ ] 메뉴별 사용자 권한 조회 API
- [ ] 메뉴별 사용자 권한 설정/수정 API
- [ ] ManageMenuService에 권한 관련 메서드 추가

### 3단계: API 접근 제어 (1주)
- [ ] MenuBasedAccessInterceptor 생성
- [ ] API 패턴 매칭 및 권한 검증 로직
- [ ] 기존 API에 권한 검증 적용

---

## 🌐 프론트엔드 API 가이드

### 기본 정보
- **Base URL**: `/manage/menus`
- **인증**: JWT Bearer Token 필요
- **권한**: SUPER_ADMIN 권한 필요
- **Content-Type**: `application/json`

---

## 📡 핵심 API 엔드포인트 (메뉴 상세페이지용)

### 1. 메뉴별 사용자 권한 관리

#### 1.1 특정 메뉴의 사용자별 권한 조회
**기능**: 특정 메뉴에 대한 모든 사용자의 CRUD 권한을 조회합니다. (메뉴 상세페이지 핵심 기능)

```http
GET /manage/menus/{menuId}/users/permissions?page=0&size=10&search=<EMAIL>
```

**경로 변수**:
- `menuId`: 메뉴 ID

**쿼리 파라미터**:
- `page`: 페이지 번호 (0부터 시작)
- `size`: 페이지 크기 (기본값: 10)
- `search`: 사용자 이메일 검색어 (선택사항)

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "menuCode": "QR_MANAGEMENT",
    "menuName": "QR 관리",
    "userPermissions": {
      "content": [
        {
          "userEmail": "<EMAIL>",
          "userName": "사용자A",
          "roleId": "SUB_ADMIN",
          "roleName": "서브 관리자",
          "canRead": "Y",
          "canWrite": "N",
          "canUpdate": "N",
          "canDelete": "N",
          "permissionNote": "읽기 전용",
          "permissionSource": "default",
          "lastUpdateDate": "2025-01-01 10:00:00"
        },
        {
          "userEmail": "<EMAIL>",
          "userName": "사용자B",
          "roleId": "VIEWER",
          "roleName": "뷰어",
          "canRead": "Y",
          "canWrite": "N",
          "canUpdate": "Y",
          "canDelete": "Y",
          "permissionNote": "QR 관리 특별 권한",
          "permissionSource": "custom",
          "lastUpdateDate": "2025-01-01 10:00:00"
        }
      ],
      "totalElements": 2,
      "totalPages": 1,
      "size": 10,
      "number": 0
    }
  },
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

**permissionSource 값**:
- `default`: 기본 권한 (역할 기반 또는 전역 기본값)
- `custom`: 개별 설정된 권한

#### 1.2 메뉴별 사용자 권한 설정/수정
**기능**: 특정 메뉴에 대한 개별 사용자의 CRUD 권한을 설정합니다.

```http
POST /manage/menus/{menuId}/users/{userEmail}/permissions
PUT /manage/menus/{menuId}/users/{userEmail}/permissions
```

**경로 변수**:
- `menuId`: 메뉴 ID
- `userEmail`: 대상 사용자 이메일

**요청 형식**:
```json
{
  "canRead": "Y",
  "canWrite": "N",
  "canUpdate": "Y",
  "canDelete": "Y",
  "permissionNote": "QR 관리 특별 권한 부여"
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "userEmail": "<EMAIL>",
    "canRead": "Y",
    "canWrite": "N",
    "canUpdate": "Y",
    "canDelete": "Y",
    "permissionNote": "QR 관리 특별 권한 부여"
  },
  "message": "메뉴 사용자 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 1.3 메뉴별 사용자 권한 삭제 (기본값으로 복원)
**기능**: 특정 메뉴의 개별 사용자 권한을 삭제하여 기본값으로 복원합니다.

```http
DELETE /manage/menus/{menuId}/users/{userEmail}/permissions
```

**응답 형식**:
```json
{
  "success": true,
  "data": null,
  "message": "메뉴 사용자 권한이 삭제되어 기본값으로 복원되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 1.4 메뉴별 사용자 권한 일괄 설정
**기능**: 특정 메뉴에 대한 여러 사용자의 권한을 일괄로 설정합니다.

```http
POST /manage/menus/{menuId}/users/permissions/batch
```

**요청 형식**:
```json
{
  "userPermissions": [
    {
      "userEmail": "<EMAIL>",
      "canRead": "Y",
      "canWrite": "N",
      "canUpdate": "N",
      "canDelete": "N",
      "permissionNote": "읽기 전용"
    },
    {
      "userEmail": "<EMAIL>",
      "canRead": "Y",
      "canWrite": "N",
      "canUpdate": "Y",
      "canDelete": "Y",
      "permissionNote": "QR 관리 특별 권한"
    }
  ]
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "updatedUsers": ["<EMAIL>", "<EMAIL>"],
    "totalUpdated": 2
  },
  "message": "메뉴 사용자 권한이 일괄 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
    {
      "roleId": "PROJECT_ADMIN",
      "isAccessible": "Y",
      "canRead": "Y",
      "canWrite": "Y",
      "canUpdate": "Y",
      "canDelete": "Y"
    },
    {
      "roleId": "SUB_ADMIN",
      "isAccessible": "Y",
      "canRead": "Y",
      "canWrite": "Y",
      "canUpdate": "N",
      "canDelete": "N"
    }
  ]
}
```

**응답 형식**:
```json
{
  "success": true,
  "data": {
    "menuId": 1,
    "updatedRoles": ["PROJECT_ADMIN", "SUB_ADMIN"],
    "totalUpdated": 2
  },
  "message": "메뉴 역할 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

---

## 🎨 프론트엔드 구현 가이드

### 메뉴 상세페이지 UI 구조
```
📋 QR 관리 메뉴 상세
├── 📊 메뉴 기본 정보 (메뉴명, 코드, 설명)
└── 👥 사용자별 권한 관리
    ├── 🔍 사용자 검색/필터
    ├── 📋 권한 테이블
    │   ├── 사용자명 | 이메일 | 역할 | R | C | U | D | 메모 | 액션
    │   └── 체크박스로 CRUD 권한 개별 설정
    └── 💾 일괄 저장 버튼
```

### 권한 체크 유틸리티
```javascript
class MenuPermissionManager {
  constructor(menuId) {
    this.menuId = menuId;
    this.users = [];
  }

  async loadUsers(page = 0, search = '') {
    const response = await fetch(
      `/manage/menus/${this.menuId}/users/permissions?page=${page}&search=${search}`
    );
    const data = await response.json();
    this.users = data.data.userPermissions.content;
    return data;
  }

  async updateUserPermission(userEmail, permissions) {
    const response = await fetch(
      `/manage/menus/${this.menuId}/users/${userEmail}/permissions`,
      {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(permissions)
      }
    );
    return response.json();
  }

  async batchUpdatePermissions(userPermissions) {
    const response = await fetch(
      `/manage/menus/${this.menuId}/users/permissions/batch`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userPermissions })
      }
    );
    return response.json();
  }
}
```

---

## 🚀 구현 순서 (간소화)

### Phase 1: 기반 구조 (1주)
1. 기존 테이블에 CRUD 권한 컬럼 추가
2. 메뉴-API 매핑 테이블 생성
3. MenuDto 확장

### Phase 2: 메뉴별 사용자 권한 API (1주)
1. 메뉴별 사용자 권한 조회 API
2. 메뉴별 사용자 권한 설정/수정/삭제 API
3. 일괄 설정 API

### Phase 3: API 접근 제어 (1주)
1. 인터셉터 구현
2. 권한 검증 로직
3. 테스트 및 최적화

---

## 📝 주요 변경사항

### ❌ 제거된 기능 (불필요)
- 전역 기본 권한 관리 API
- 사용자별 전역 권한 관리 API
- 메뉴별 역할 권한 관리 API
- 복잡한 권한 매트릭스 API

### ✅ 핵심 기능만 유지
- **메뉴 상세페이지에서 사용자별 CRUD 권한 관리**
- 간단한 조회/설정/수정/삭제 API
- 일괄 설정 기능
- API 접근 제어

### 🎯 구현 목표 단순화
**"관리자가 특정 메뉴 선택 → 해당 메뉴의 모든 사용자 권한 관리"** 기능에만 집중



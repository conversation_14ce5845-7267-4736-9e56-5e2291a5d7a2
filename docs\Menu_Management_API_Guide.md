# 메뉴 관리 API 가이드

## 개요
메뉴 관리 시스템의 API 엔드포인트와 사용법을 설명합니다.

## 기본 정보
- **Base URL**: `/api/way/manage/menus`
- **인증**: JWT Bearer Token 필요
- **Content-Type**: `application/json`

---

## 1. 일반 사용자용 API

### 1.1 현재 사용자의 접근 가능한 메뉴 트리 조회
현재 로그인한 사용자가 접근 가능한 메뉴 트리를 조회합니다.

**요청**
```
GET /api/way/manage/menus/accessible
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": [
    {
      "menuId": 1,
      "parentMenuId": null,
      "menuCode": "DASHBOARD",
      "menuName": "대시보드",
      "menuUrl": "/dashboard",
      "menuIcon": "fas fa-tachometer-alt",
      "menuLevel": 1,
      "displayOrder": 1,
      "status": "ACTIVE",
      "createDate": "2024-01-01 10:00:00",
      "accessible": true,
      "children": [
        {
          "menuId": 2,
          "parentMenuId": 1,
          "menuCode": "QR_MANAGEMENT",
          "menuName": "QR 관리",
          "menuUrl": "/qr",
          "menuIcon": "fas fa-qrcode",
          "menuLevel": 2,
          "displayOrder": 1,
          "status": "ACTIVE",
          "createDate": "2024-01-01 10:00:00",
          "accessible": true,
          "children": []
        }
      ]
    }
  ],
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

---

## 2. SUPER_ADMIN 전용 메뉴 관리 API

### 2.1 메뉴 생성
새로운 메뉴를 생성합니다.

**요청**
```
POST /api/way/manage/menus
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json

{
  "parentMenuId": 1,
  "menuCode": "NEW_MENU",
  "menuName": "새 메뉴",
  "menuUrl": "/new-menu",
  "menuIcon": "fas fa-plus",
  "menuLevel": 2,
  "displayOrder": 1,
  "status": "ACTIVE"
}
```

**응답**
```json
{
  "success": true,
  "data": 3,
  "message": "메뉴가 성공적으로 생성되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 2.2 메뉴 수정
기존 메뉴 정보를 수정합니다.

**요청**
```
PUT /api/way/manage/menus/{menuId}
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json

{
  "parentMenuId": 1,
  "menuName": "수정된 메뉴",
  "menuUrl": "/updated-menu",
  "menuIcon": "fas fa-edit",
  "menuLevel": 2,
  "displayOrder": 2,
  "status": "ACTIVE"
}
```

**응답**
```json
{
  "success": true,
  "data": null,
  "message": "메뉴가 성공적으로 수정되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 2.3 메뉴 삭제
메뉴를 삭제합니다.

**요청**
```
DELETE /api/way/manage/menus/{menuId}
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": null,
  "message": "메뉴가 성공적으로 삭제되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 2.4 전체 메뉴 트리 조회
전체 메뉴 트리 구조를 조회합니다.

**요청**
```
GET /api/way/manage/menus/tree
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": [
    {
      "menuId": 1,
      "parentMenuId": null,
      "menuCode": "DASHBOARD",
      "menuName": "대시보드",
      "menuUrl": "/dashboard",
      "menuIcon": "fas fa-tachometer-alt",
      "menuLevel": 1,
      "displayOrder": 1,
      "status": "ACTIVE",
      "createDate": "2024-01-01 10:00:00",
      "accessible": true,
      "children": []
    }
  ],
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

---

## 3. 권한 관리 API

### 3.1 메뉴 역할 권한 설정
특정 메뉴에 대한 역할별 접근 권한을 설정합니다.

**요청**
```
POST /api/way/manage/menus/{menuId}/roles/{roleId}/permissions?isAccessible=Y
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": null,
  "message": "권한이 성공적으로 설정되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 3.2 메뉴 사용자 권한 설정
특정 메뉴에 대한 개별 사용자 접근 권한을 설정합니다.

**요청**
```
POST /api/way/manage/menus/{menuId}/users/{userEmail}/permissions
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json

{
  "isAccessible": "Y",
  "permissionNote": "특별 권한 부여"
}
```

**응답**
```json
{
  "success": true,
  "data": null,
  "message": "권한이 성공적으로 설정되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

---

## 4. 사용자별 메뉴 조회 API (SUPER_ADMIN 전용)

### 4.1 특정 사용자의 접근 가능한 메뉴 트리 조회
특정 사용자가 접근 가능한 메뉴 트리를 조회합니다.

**요청**
```
GET /api/way/manage/menus/users/{userEmail}/accessible
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": [
    {
      "menuId": 1,
      "parentMenuId": null,
      "menuCode": "DASHBOARD",
      "menuName": "대시보드",
      "menuUrl": "/dashboard",
      "menuIcon": "fas fa-tachometer-alt",
      "menuLevel": 1,
      "displayOrder": 1,
      "status": "ACTIVE",
      "createDate": "2024-01-01 10:00:00",
      "accessible": true,
      "children": []
    }
  ],
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 4.2 특정 사용자의 메뉴 접근 권한 확인
특정 사용자가 특정 메뉴에 접근 가능한지 확인합니다.

**요청**
```
GET /api/way/manage/menus/{menuId}/users/{userEmail}/access
Authorization: Bearer {JWT_TOKEN}
```

**응답**
```json
{
  "success": true,
  "data": true,
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2024-01-01T10:00:00"
}
```

---

## 5. 공통 응답 구조

### 성공 응답
```json
{
  "success": true,
  "data": {}, // 실제 데이터
  "message": "성공 메시지",
  "timestamp": "2024-01-01T10:00:00"
}
```

### 오류 응답
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "오류 메시지"
  },
  "timestamp": "2024-01-01T10:00:00"
}
```

---

## 6. 권한 요구사항

| API | 필요 권한 | 설명 |
|-----|----------|------|
| GET /accessible | 인증된 사용자 | 현재 사용자의 메뉴 조회 |
| POST / | SUPER_ADMIN | 메뉴 생성 |
| PUT /{menuId} | SUPER_ADMIN | 메뉴 수정 |
| DELETE /{menuId} | SUPER_ADMIN | 메뉴 삭제 |
| GET /tree | SUPER_ADMIN | 전체 메뉴 트리 조회 |
| POST /{menuId}/roles/{roleId}/permissions | SUPER_ADMIN | 역할 권한 설정 |
| POST /{menuId}/users/{userEmail}/permissions | SUPER_ADMIN | 사용자 권한 설정 |
| GET /users/{userEmail}/accessible | SUPER_ADMIN | 특정 사용자 메뉴 조회 |
| GET /{menuId}/users/{userEmail}/access | SUPER_ADMIN | 사용자 접근 권한 확인 |

---

## 7. 주요 변경사항

✅ **추가된 API**: `GET /api/way/manage/menus/accessible`
- 현재 로그인한 사용자의 접근 가능한 메뉴 트리를 조회하는 API가 추가되었습니다.
- 프론트엔드에서 사용자별 메뉴를 동적으로 구성할 때 사용하세요.

---

## 8. 사용 예시

### 프론트엔드에서 메뉴 구성하기
```javascript
// 현재 사용자의 메뉴 조회
const response = await fetch('/api/way/manage/menus/accessible', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
if (result.success) {
  const menuTree = result.data;
  // 메뉴 트리를 사용하여 네비게이션 구성
  buildNavigation(menuTree);
}
```

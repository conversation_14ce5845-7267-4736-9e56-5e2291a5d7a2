package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum ErrorCode {

    // Common Errors (예시, 필요에 따라 조정)
    INVALID_INPUT_VALUE(HttpStatus.BAD_REQUEST, "C001", "입력 값이 올바르지 않습니다."),
    INTERNAL_SERVER_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "C002", "서버 내부 오류가 발생했습니다."),
    METHOD_NOT_ALLOWED(HttpStatus.METHOD_NOT_ALLOWED, "C003", "허용되지 않은 메소드입니다."),
    ENTITY_NOT_FOUND(HttpStatus.NOT_FOUND, "C004", "대상을 찾을 수 없습니다."),
    HANDLE_ACCESS_DENIED(HttpStatus.FORBIDDEN, "C005", "접근이 거부되었습니다."),
    INVALID_TYPE_VALUE(HttpStatus.BAD_REQUEST, "C006", "잘못된 타입 값입니다."),
    INVALID_DATE_RANGE(HttpStatus.BAD_REQUEST, "C007", "시작일이 종료일보다 늦거나 유효하지 않은 날짜 범위입니다."),
    INVALID_SORT_PARAMETER(HttpStatus.BAD_REQUEST, "C011", "유효하지 않은 정렬 파라미터입니다."),

    // Authentication & Authorization Errors
    AUTHENTICATION_FAILED(HttpStatus.UNAUTHORIZED, "A001", "아이디 또는 비밀번호가 잘못되었습니다."),
    INVALID_TOKEN(HttpStatus.UNAUTHORIZED, "A002", "유효하지 않은 토큰입니다."),
    EXPIRED_TOKEN(HttpStatus.UNAUTHORIZED, "A003", "만료된 토큰입니다."),
    ACCESS_DENIED(HttpStatus.FORBIDDEN, "A004", "접근 권한이 없습니다."),
    UNAUTHENTICATED(HttpStatus.UNAUTHORIZED, "A005", "인증되지 않은 사용자입니다."),
    ACCOUNT_DISABLED(HttpStatus.FORBIDDEN, "A006", "비활성화된 계정입니다."),

    // User Errors
    USER_NOT_FOUND(HttpStatus.NOT_FOUND, "U001", "사용자를 찾을 수 없습니다."),
    EMAIL_DUPLICATION(HttpStatus.CONFLICT, "U002", "이미 사용 중인 이메일입니다."),
    LOGIN_INPUT_INVALID(HttpStatus.BAD_REQUEST, "U003", "로그인 정보가 올바르지 않습니다."),

    // Attendee Errors
    ATTENDEE_NOT_FOUND(HttpStatus.NOT_FOUND, "A101", "참석자를 찾을 수 없습니다."),
    ALREADY_ATTENDED(HttpStatus.CONFLICT, "A102", "이미 참석한 참석자입니다."),
    ATTENDANCE_NOT_ALLOWED(HttpStatus.FORBIDDEN, "A103", "참석 불가능한 인원입니다."),
    ATTENDANCE_NOT_PERMITTED(HttpStatus.FORBIDDEN, "A104", "아직 참석 처리 되지 않은 인원입니다."),
    ATTENDEE_NOT_CONFIRMED(HttpStatus.FORBIDDEN, "A105", "참석이 불가능한 인원입니다."),
    ATTENDEE_ALREADY_REGISTERED(HttpStatus.CONFLICT, "A106", "이미 등록된 참석자입니다."), 
    ATTENDEE_REGISTRATION_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "A107", "참석자 등록에 실패했습니다."), 
    ATTENDEE_REGISTRATION_FAILED_DUPLICATE(HttpStatus.CONFLICT, "A108", "참석자 등록 실패: 중복된 항목입니다."), 
    JSON_PROCESSING_ERROR(HttpStatus.BAD_REQUEST, "A109", "JSON 처리 중 오류가 발생했습니다."), 

    // Benefit/Redemption Errors
    BENEFIT_NOT_FOUND(HttpStatus.NOT_FOUND, "B001", "혜택을 찾을 수 없습니다."),
    BENEFIT_ALREADY_REDEEMED(HttpStatus.CONFLICT, "B002", "이미 사용된 혜택입니다."),
    BENEFIT_JSON_PARSE_ERROR(HttpStatus.BAD_REQUEST, "B011", "혜택 정보(JSON) 형식이 잘못되었습니다."),
    BENEFIT_CODE_DUPLICATE_IN_REQUEST(HttpStatus.BAD_REQUEST, "B012", "요청 내에 동일한 혜택 코드가 존재합니다."),
    BENEFIT_CODE_ALREADY_EXISTS(HttpStatus.CONFLICT, "B013", "이미 해당 이벤트에 동일한 코드를 가진 혜택이 존재합니다."),
    BENEFIT_OUT_OF_STOCK(HttpStatus.CONFLICT, "B014", "혜택 재고가 부족합니다."),
    BENEFIT_INCONSISTENCY_DETECTED(HttpStatus.BAD_REQUEST, "B015", "이벤트의 기존 혜택이 요청에 누락되었습니다."),
    BENEFIT_NOT_FOUND_IN_EVENT(HttpStatus.BAD_REQUEST, "B016", "해당 이벤트에 존재하지 않는 혜택입니다."),

    // Redemption Errors
    REDEMPTION_NOT_FOUND(HttpStatus.NOT_FOUND, "R001", "혜택 사용 기록을 찾을 수 없습니다."),

    // QR Code Errors
    QR_CODE_NOT_FOUND(HttpStatus.NOT_FOUND, "Q001", "QR 코드를 찾을 수 없습니다."),
    QR_CODE_GENERATION_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "Q002", "QR 코드 생성 중 오류가 발생했습니다."),
    QR_CODE_FILE_SAVE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "Q003", "QR 코드 이미지 파일 저장 중 오류가 발생했습니다."),
    QR_CODE_SAVE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "Q004", "QR 코드 정보 저장 중 오류가 발생했습니다."),
    QR_CODE_UPDATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "Q005", "QR 코드 정보 업데이트 중 오류가 발생했습니다."),
    QR_CODE_NOT_ACTIVE(HttpStatus.FORBIDDEN, "Q006", "QR 코드가 활성 상태가 아닙니다."),
    QR_CODE_EXPIRED(HttpStatus.FORBIDDEN, "Q007", "QR 코드의 유효 기간이 아닙니다."),
    DUPLICATE_QR_NAME(HttpStatus.CONFLICT, "Q008", "해당 프로젝트에 동일한 이름의 QR 코드가 이미 존재합니다."),
    FILE_PROCESSING_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "Q009", "파일 처리 중 오류가 발생했습니다."), 
    QR_CODE_PROJECT_MISMATCH(HttpStatus.FORBIDDEN, "Q010", "다른 프로젝트 QR은 스캔할 수 없습니다"),
    QR_CODE_ALREADY_EXISTS(HttpStatus.CONFLICT, "Q011", "이미 QR 코드가 존재합니다."), 
    QR_CODE_EVENT_MISMATCH(HttpStatus.BAD_REQUEST, "Q012", "QR 코드와 이벤트 정보가 일치하지 않습니다."), 
    QR_CODE_INVALID_FORMAT(HttpStatus.BAD_REQUEST, "Q013", "유효하지 않은 QR 코드 형식입니다."), 
    QR_CODE_REGISTRATION_FAILED_DUPLICATE(HttpStatus.CONFLICT, "Q014", "QR 코드 등록 실패: 중복된 항목입니다."),

    // Project Errors
    PROJECT_NOT_FOUND(HttpStatus.NOT_FOUND, "P001", "프로젝트를 찾을 수 없습니다."),
    PROJECT_NAME_DUPLICATION(HttpStatus.CONFLICT, "P002", "이미 사용 중인 프로젝트 이름입니다."),

    // Event Errors
    EVENT_NOT_FOUND(HttpStatus.NOT_FOUND, "E001", "이벤트를 찾을 수 없습니다."),
    EVENT_CREATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "E002", "이벤트 생성 중 오류가 발생했습니다."),
    EVENT_NAME_DUPLICATION(HttpStatus.CONFLICT, "E003", "이벤트 이름이 중복되었습니다."),
    EVENT_PARTICIPANT_LIMIT_EXCEEDED(HttpStatus.BAD_REQUEST, "E004", "이벤트 참여 가능한 인원이 마감되었습니다."),
    EVENT_NOT_IN_PROGRESS(HttpStatus.BAD_REQUEST, "E005", "이벤트 진행 기간이 아닙니다."),

    // Pre-registration Form Errors
    PRE_REG_FORM_NOT_FOUND(HttpStatus.NOT_FOUND, "F001", "신청서 양식을 찾을 수 없습니다."),
    PRE_REG_FORM_CREATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "F002", "신청서 양식 생성 중 오류가 발생했습니다."),
    PRE_REG_FORM_NAME_DUPLICATION(HttpStatus.CONFLICT, "F003", "양식 이름이 중복되었습니다."),

    // Statistics Errors
    STATISTICS_QUERY_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "S001", "통계 데이터 조회 중 오류가 발생했습니다."),
    STATISTICS_UNEXPECTED_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "S002", "통계 처리 중 예상치 못한 오류가 발생했습니다."),

    // Inquiry Errors
    INQUIRY_NOT_FOUND(HttpStatus.NOT_FOUND, "I001", "문의 정보를 찾을 수 없습니다."),
    INQUIRY_ACCESS_DENIED(HttpStatus.FORBIDDEN, "I002", "문의에 대한 접근 권한이 없습니다."),
    INQUIRY_COMMENT_NOT_FOUND(HttpStatus.NOT_FOUND, "I003", "문의 댓글을 찾을 수 없습니다."),
    FILE_UPLOAD_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "I004", "파일 업로드에 실패했습니다."),

    // Notification Errors
    NOTIFICATION_NOT_FOUND(HttpStatus.NOT_FOUND, "N001", "알림 정보를 찾을 수 없습니다."),

    // External Service Errors
    THYMELEAF_SERVER_COMM_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "T501", "콘텐츠 서버와 통신에 실패했습니다."),

    // Team Errors
    TEAM_NOT_FOUND(HttpStatus.NOT_FOUND, "T001", "팀을 찾을 수 없습니다."),
    TEAM_CREATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "T002", "팀 생성 중 오류가 발생했습니다."),
    TEAM_UPDATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "T003", "팀 수정 중 오류가 발생했습니다."),
    TEAM_DELETE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "T004", "팀 삭제 중 오류가 발생했습니다."),
    DUPLICATE_TEAM_NAME(HttpStatus.CONFLICT, "T005", "이미 이벤트 내에 동일한 팀명이 존재합니다."),
    DUPLICATE_TEAM_CODE(HttpStatus.CONFLICT, "T006", "이미 동일한 팀 코드가 존재합니다."),
    TEAM_FULL(HttpStatus.CONFLICT, "T007", "팀 정원이 가득 찼습니다."),

    // Menu Errors
    MENU_NOT_FOUND(HttpStatus.NOT_FOUND, "M001", "메뉴를 찾을 수 없습니다."),
    DUPLICATE_MENU_CODE(HttpStatus.CONFLICT, "M002", "이미 존재하는 메뉴 코드입니다."),
    DUPLICATE_MENU_NAME(HttpStatus.CONFLICT, "M003", "이미 존재하는 메뉴 이름입니다."),
    MENU_CREATION_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "M004", "메뉴 생성에 실패했습니다."),
    MENU_UPDATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "M005", "메뉴 수정에 실패했습니다."),
    MENU_DELETE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "M006", "메뉴 삭제에 실패했습니다."),
    MENU_HAS_CHILDREN(HttpStatus.CONFLICT, "M007", "하위 메뉴가 있는 메뉴는 삭제할 수 없습니다."),
    PARENT_MENU_NOT_ACTIVE(HttpStatus.BAD_REQUEST, "M008", "상위 메뉴가 비활성화 상태입니다."),
    INVALID_PARENT_MENU(HttpStatus.BAD_REQUEST, "M008", "유효하지 않은 상위 메뉴입니다."),
    CIRCULAR_REFERENCE(HttpStatus.BAD_REQUEST, "M009", "순환 참조가 발생할 수 있는 메뉴 구조입니다."),

    // Menu Permission Errors
    PERMISSION_NOT_FOUND(HttpStatus.NOT_FOUND, "MP001", "권한 정보를 찾을 수 없습니다."),
    PERMISSION_CREATION_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "MP002", "권한 생성에 실패했습니다."),
    PERMISSION_UPDATE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "MP003", "권한 수정에 실패했습니다."),
    PERMISSION_DELETE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, "MP004", "권한 삭제에 실패했습니다."),

    // 일반 데이터베이스 오류
    DATABASE_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "DB001", "데이터베이스 처리 중 오류가 발생했습니다."),
    DATABASE_DUPLICATE_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "DB002", "데이터베이스 중복 오류가 발생했습니다.");


    private final HttpStatus status;
    private final String code;
    private final String message;

    ErrorCode(HttpStatus status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}